#!/usr/bin/env python3
"""
测试参会人音频识别功能
"""

import requests
import json
import numpy as np
import soundfile as sf
import tempfile
import os
from pathlib import Path

def create_test_audio(duration=3, sample_rate=16000, frequency=440):
    """创建测试音频文件"""
    t = np.linspace(0, duration, int(sample_rate * duration))
    # 创建不同频率的正弦波来模拟不同的说话人
    audio = np.sin(2 * np.pi * frequency * t) * 0.5
    return audio.astype(np.float32)

def save_audio_file(audio_data, filename, sample_rate=16000):
    """保存音频文件"""
    sf.write(filename, audio_data, sample_rate)
    return filename

def test_offline_post_api():
    """测试离线POST接口的参会人音频功能"""
    print("🧪 测试离线POST接口...")
    
    # 创建测试音频文件
    main_audio = create_test_audio(duration=5, frequency=440)  # 主音频
    participant1_audio = create_test_audio(duration=2, frequency=440)  # 参会人1（相同频率）
    participant2_audio = create_test_audio(duration=2, frequency=880)  # 参会人2（不同频率）
    
    # 保存为临时文件
    with tempfile.TemporaryDirectory() as temp_dir:
        main_file = os.path.join(temp_dir, "main_audio.wav")
        participant1_file = os.path.join(temp_dir, "张三.wav")
        participant2_file = os.path.join(temp_dir, "李四.wav")
        
        save_audio_file(main_audio, main_file)
        save_audio_file(participant1_audio, participant1_file)
        save_audio_file(participant2_audio, participant2_file)
        
        # 准备请求数据
        files = {
            'audio': open(main_file, 'rb'),
            'participant_张三': open(participant1_file, 'rb'),
            'participant_李四': open(participant2_file, 'rb')
        }
        
        data = {
            'enable_speaker': 'true'
        }
        
        try:
            # 发送请求
            response = requests.post('http://localhost:5000/transcribe', files=files, data=data)
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.json()}")
            
            if response.status_code == 200:
                result = response.json()
                if 'participant_results' in result.get('data', {}):
                    print("✅ 参会人音频处理成功")
                    print(f"参会人处理结果: {result['data']['participant_results']}")
                else:
                    print("⚠️ 响应中没有参会人处理结果")
            else:
                print(f"❌ 请求失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
        finally:
            # 关闭文件
            for file_obj in files.values():
                file_obj.close()

def test_websocket_api():
    """测试WebSocket接口的参会人音频功能"""
    print("🧪 测试WebSocket接口...")
    
    try:
        import socketio
        
        # 创建Socket.IO客户端
        sio = socketio.Client()
        
        @sio.event
        def connect():
            print("✅ WebSocket连接成功")
            
        @sio.event
        def realtime_started(data):
            print(f"✅ 实时转录开始: {data}")
            if 'participant_results' in data.get('data', {}):
                print(f"参会人处理结果: {data['data']['participant_results']}")
            
        @sio.event
        def realtime_error(data):
            print(f"❌ 实时转录错误: {data}")
            
        @sio.event
        def participant_audio_success(data):
            print(f"✅ 参会人音频上传成功: {data}")
            
        @sio.event
        def participant_audio_error(data):
            print(f"❌ 参会人音频上传失败: {data}")
        
        # 连接到服务器
        sio.connect('http://localhost:5000')
        
        # 开始实时转录（不带参会人音频）
        sio.emit('start_realtime', {
            'enable_speaker': True
        })
        
        # 等待一段时间
        sio.sleep(2)
        
        # 断开连接
        sio.disconnect()
        print("✅ WebSocket测试完成")
        
    except ImportError:
        print("⚠️ 需要安装python-socketio库: pip install python-socketio[client]")
    except Exception as e:
        print(f"❌ WebSocket测试失败: {str(e)}")

def test_api_compatibility():
    """测试API向后兼容性"""
    print("🧪 测试API向后兼容性...")
    
    # 创建简单的测试音频
    test_audio = create_test_audio(duration=3)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        audio_file = os.path.join(temp_dir, "test_audio.wav")
        save_audio_file(test_audio, audio_file)
        
        # 测试不带参会人音频的请求
        files = {'audio': open(audio_file, 'rb')}
        data = {'enable_speaker': 'false'}
        
        try:
            response = requests.post('http://localhost:5000/transcribe', files=files, data=data)
            
            print(f"向后兼容性测试 - 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 向后兼容性测试通过")
                print(f"响应数据: {result}")
            else:
                print(f"❌ 向后兼容性测试失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 向后兼容性测试异常: {str(e)}")
        finally:
            files['audio'].close()

if __name__ == "__main__":
    print("🚀 开始测试参会人音频识别功能...")
    print("请确保服务器正在运行 (python app.py)")
    print("-" * 50)
    
    # 测试向后兼容性
    test_api_compatibility()
    print("-" * 50)
    
    # 测试离线POST接口
    test_offline_post_api()
    print("-" * 50)
    
    # 测试WebSocket接口
    test_websocket_api()
    print("-" * 50)
    
    print("✅ 所有测试完成")
