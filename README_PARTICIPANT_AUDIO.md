# 会话级参会人音频识别功能

## 功能概述

本功能支持在会话创建时上传参会人的音频样本，系统会在该会话的转录过程中自动识别这些参会人的声音，并在转录结果中显示具体的参会人姓名，而不是默认的 Speaker_0、Speaker_1 等标识。

## 主要特性

- **会话级管理**: 参会人音频特征存储在会话级别，会话结束时自动清理
- **智能识别**: 基于3D-Speaker模型进行说话人特征提取和匹配
- **向后兼容**: 不上传参会人音频时，仍使用默认的Speaker_X标识
- **多接口支持**: 支持实时WebSocket、离线WebSocket和离线POST三种接口

## 使用方法

### 1. 离线POST接口

```bash
curl -X POST http://localhost:5000/transcribe \
  -F "audio=@meeting.wav" \
  -F "enable_speaker=true" \
  -F "participant_0=@张三.wav" \
  -F "participant_1=@李四.wav"
```

**参数说明:**
- `audio`: 主音频文件
- `enable_speaker`: 启用说话人识别
- `participant_X`: 参会人音频文件，以姓名命名

### 2. 实时WebSocket接口

```javascript
const socket = io();

// 准备参会人音频文件
const participantAudios = {
  '张三.wav': file1,
  '李四.wav': file2
};

// 开始实时转录
socket.emit('start_realtime', {
  enable_speaker: true,
  participant_audios: participantAudios
});

// 监听结果
socket.on('realtime_started', (data) => {
  console.log('参会人处理结果:', data.data.participant_results);
});
```

### 3. 离线WebSocket接口

```javascript
const socket = io();

// 开始离线转录
socket.emit('start_offline', {
  enable_speaker: true,
  participant_audios: participantAudios,
  filename: 'meeting.wav',
  file_size: 1024000
});
```

## 响应格式

### 成功响应

```json
{
  "code": 200,
  "message": "转录成功",
  "data": {
    "voice_text": "完整转录文本",
    "segmented_text": [
      {
        "start_time": 0.0,
        "end_time": 3.5,
        "speaker": "张三",
        "text": "大家好，我是张三"
      },
      {
        "start_time": 3.5,
        "end_time": 7.2,
        "speaker": "李四", 
        "text": "你好张三，我是李四"
      }
    ],
    "participant_results": {
      "张三": true,
      "李四": true
    }
  }
}
```

### 参会人处理结果说明

- `participant_results`: 显示每个参会人音频的处理状态
  - `true`: 音频特征提取成功
  - `false`: 音频特征提取失败

## 技术实现

### 架构设计

1. **会话级存储**: 参会人特征存储在 `session_participants[session_id][participant_name]`
2. **特征提取**: 使用3D-Speaker模型提取音频特征向量
3. **相似度匹配**: 通过余弦相似度进行说话人识别
4. **自动清理**: 会话结束时自动清理参会人数据

### 关键组件

- `ParticipantAudioManager`: 参会人音频管理器
- `RealtimeAudioTranscriptionService`: 实时转录服务
- `OfflineAudioTranscriptionService`: 离线转录服务

### 配置参数

- `similarity_threshold`: 相似度阈值，默认0.6
- 音频预处理: 自动转换为16kHz单声道
- 特征向量: 基于3D-Speaker模型

## 测试方法

### 1. 启动测试服务器

```bash
python start_test_server.py
```

### 2. 访问测试页面

- 主页面: http://localhost:5000/
- 会话级测试: http://localhost:5000/test_session_participants.html
- 简单测试: http://localhost:5000/test_simple.html

### 3. 准备测试音频

1. 准备主音频文件（包含多人对话）
2. 准备参会人音频样本，以姓名命名（如：张三.wav）
3. 上传并测试识别效果

## 注意事项

1. **音频质量**: 参会人音频样本质量越高，识别准确率越高
2. **文件命名**: 参会人音频文件名即为识别后显示的姓名
3. **会话隔离**: 不同会话的参会人数据完全隔离
4. **内存管理**: 会话结束时自动清理，避免内存泄漏
5. **向后兼容**: 不影响现有功能，可选择性使用

## 故障排除

### 常见问题

1. **特征提取失败**
   - 检查音频文件格式和质量
   - 确保3D-Speaker模型正确加载

2. **识别准确率低**
   - 提高参会人音频样本质量
   - 调整相似度阈值

3. **内存占用过高**
   - 检查会话清理逻辑
   - 监控参会人数据存储

### 调试方法

1. 查看服务器日志中的特征提取信息
2. 检查参会人处理结果
3. 监控会话清理日志

## 更新日志

- v1.0: 初始版本，支持会话级参会人音频识别
- 支持实时WebSocket、离线WebSocket、离线POST三种接口
- 自动会话清理和内存管理
