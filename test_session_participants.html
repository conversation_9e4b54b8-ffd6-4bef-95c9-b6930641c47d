<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会话级参会人音频识别测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; background-color: #d4edda; border-color: #c3e6cb; }
        .error { color: #dc3545; background-color: #f8d7da; border-color: #f5c6cb; }
        .info { color: #0c5460; background-color: #d1ecf1; border-color: #bee5eb; }
        .participant-file {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            gap: 10px;
        }
        .participant-file input {
            flex: 1;
        }
        .participant-file button {
            padding: 5px 10px;
            font-size: 12px;
        }
        .session-info {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎙️ 会话级参会人音频识别测试</h1>
        
        <div class="session-info">
            <strong>会话状态:</strong> <span id="sessionStatus">未连接</span><br>
            <strong>会话ID:</strong> <span id="sessionId">-</span><br>
            <strong>参会人数量:</strong> <span id="participantCount">0</span>
        </div>

        <!-- 离线转录测试 -->
        <h2>📁 离线转录测试</h2>
        <form id="offlineForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="audioFile">主音频文件:</label>
                <input type="file" id="audioFile" name="audio" accept="audio/*" required>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="enableSpeaker" checked>
                    启用说话人识别
                </label>
            </div>

            <div id="participantSection">
                <div class="form-group">
                    <label>参会人音频样本 (以姓名命名文件):</label>
                    <div id="participantFiles">
                        <div class="participant-file">
                            <input type="file" name="participant_0" accept="audio/*" placeholder="张三.wav">
                            <button type="button" onclick="addParticipantFile()">+ 添加</button>
                        </div>
                    </div>
                    <small style="color: #666;">
                        提示：将音频文件命名为参会人姓名（如：张三.wav），系统会在会话中识别这些说话人
                    </small>
                </div>
            </div>

            <button type="submit">开始离线转录</button>
        </form>

        <div id="offlineResult" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <!-- WebSocket实时转录测试 -->
        <h2>🎤 实时转录测试</h2>
        
        <div class="form-group">
            <label>参会人音频样本 (用于实时会话):</label>
            <div id="realtimeParticipantFiles">
                <div class="participant-file">
                    <input type="file" id="realtimeParticipant0" accept="audio/*" placeholder="李四.wav">
                    <button type="button" onclick="addRealtimeParticipantFile()">+ 添加</button>
                </div>
            </div>
        </div>

        <button id="startRealtimeBtn" onclick="startRealtime()">开始实时转录</button>
        <button id="stopRealtimeBtn" onclick="stopRealtime()" disabled>停止实时转录</button>

        <div id="realtimeResult" class="result" style="display: none;"></div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let participantCount = 1;
        let realtimeParticipantCount = 1;
        let socket = null;
        let isRealtimeActive = false;

        // 离线转录相关函数
        function addParticipantFile() {
            const container = document.getElementById('participantFiles');
            const newFile = document.createElement('div');
            newFile.className = 'participant-file';
            newFile.innerHTML = `
                <input type="file" name="participant_${participantCount}" accept="audio/*">
                <button type="button" onclick="removeParticipantFile(this)">- 删除</button>
            `;
            container.appendChild(newFile);
            participantCount++;
        }

        function removeParticipantFile(button) {
            button.parentElement.remove();
        }

        // 实时转录相关函数
        function addRealtimeParticipantFile() {
            const container = document.getElementById('realtimeParticipantFiles');
            const newFile = document.createElement('div');
            newFile.className = 'participant-file';
            newFile.innerHTML = `
                <input type="file" id="realtimeParticipant${realtimeParticipantCount}" accept="audio/*">
                <button type="button" onclick="removeRealtimeParticipantFile(this)">- 删除</button>
            `;
            container.appendChild(newFile);
            realtimeParticipantCount++;
        }

        function removeRealtimeParticipantFile(button) {
            button.parentElement.remove();
        }

        // 控制参会人音频区域的显示
        document.getElementById('enableSpeaker').addEventListener('change', function() {
            const section = document.getElementById('participantSection');
            section.style.display = this.checked ? 'block' : 'none';
        });

        // 离线转录表单提交
        document.getElementById('offlineForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const audioFile = document.getElementById('audioFile').files[0];
            const enableSpeaker = document.getElementById('enableSpeaker').checked;
            
            if (!audioFile) {
                showResult('offlineResult', '请选择主音频文件', 'error');
                return;
            }
            
            formData.append('audio', audioFile);
            formData.append('enable_speaker', enableSpeaker);
            
            // 添加参会人音频文件
            if (enableSpeaker) {
                const participantInputs = document.querySelectorAll('#participantFiles input[type="file"]');
                participantInputs.forEach((input, index) => {
                    if (input.files[0]) {
                        formData.append(`participant_${index}`, input.files[0]);
                    }
                });
            }
            
            showResult('offlineResult', '🔄 正在处理，请稍候...', 'info');
            
            try {
                const response = await fetch('/transcribe', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    let html = '<div class="success">✅ 转录成功</div>';
                    
                    // 显示参会人处理结果
                    if (result.data.participant_results && Object.keys(result.data.participant_results).length > 0) {
                        html += '<h3>👥 参会人音频处理结果:</h3>';
                        for (const [name, success] of Object.entries(result.data.participant_results)) {
                            const status = success ? '✅ 成功' : '❌ 失败';
                            html += `<p><strong>${name}:</strong> ${status}</p>`;
                        }
                    }
                    
                    // 显示转录结果
                    if (result.data.segmented_text && result.data.segmented_text.length > 0) {
                        html += '<h3>🎭 说话人分离结果:</h3>';
                        result.data.segmented_text.forEach(segment => {
                            html += `<div style="margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 4px;">
                                <strong>${segment.speaker}</strong> 
                                <span style="color: #666;">(${segment.start_time.toFixed(1)}s-${segment.end_time.toFixed(1)}s)</span>: 
                                ${segment.text}
                            </div>`;
                        });
                    } else {
                        html += `<h3>📝 转录结果:</h3><p>${result.data.voice_text}</p>`;
                    }
                    
                    showResult('offlineResult', html, 'success');
                } else {
                    showResult('offlineResult', `❌ 转录失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('offlineResult', `❌ 请求失败: ${error.message}`, 'error');
            }
        });

        // 实时转录函数
        function startRealtime() {
            if (isRealtimeActive) return;
            
            socket = io();
            
            socket.on('connect', function() {
                document.getElementById('sessionStatus').textContent = '已连接';
                document.getElementById('sessionId').textContent = socket.id;
                
                // 收集参会人音频文件
                const participantAudios = {};
                const realtimeInputs = document.querySelectorAll('#realtimeParticipantFiles input[type="file"]');
                
                realtimeInputs.forEach((input, index) => {
                    if (input.files[0]) {
                        participantAudios[input.files[0].name] = input.files[0];
                    }
                });
                
                // 开始实时转录
                socket.emit('start_realtime', {
                    enable_speaker: true,
                    participant_audios: participantAudios
                });
            });
            
            socket.on('realtime_started', function(data) {
                isRealtimeActive = true;
                document.getElementById('startRealtimeBtn').disabled = true;
                document.getElementById('stopRealtimeBtn').disabled = false;
                
                let html = '<div class="success">✅ 实时转录已开始</div>';
                
                if (data.data.participant_results) {
                    html += '<h3>👥 参会人音频处理结果:</h3>';
                    for (const [name, success] of Object.entries(data.data.participant_results)) {
                        const status = success ? '✅ 成功' : '❌ 失败';
                        html += `<p><strong>${name}:</strong> ${status}</p>`;
                    }
                    document.getElementById('participantCount').textContent = Object.keys(data.data.participant_results).length;
                }
                
                showResult('realtimeResult', html, 'success');
            });
            
            socket.on('realtime_error', function(data) {
                showResult('realtimeResult', `❌ 实时转录错误: ${data.message}`, 'error');
            });
            
            socket.on('disconnect', function() {
                document.getElementById('sessionStatus').textContent = '已断开';
                isRealtimeActive = false;
                document.getElementById('startRealtimeBtn').disabled = false;
                document.getElementById('stopRealtimeBtn').disabled = true;
            });
        }

        function stopRealtime() {
            if (socket) {
                socket.emit('stop_realtime');
                socket.disconnect();
                socket = null;
            }
            isRealtimeActive = false;
            document.getElementById('startRealtimeBtn').disabled = false;
            document.getElementById('stopRealtimeBtn').disabled = true;
            document.getElementById('sessionStatus').textContent = '已停止';
        }

        function showResult(elementId, content, type) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.innerHTML = content;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
