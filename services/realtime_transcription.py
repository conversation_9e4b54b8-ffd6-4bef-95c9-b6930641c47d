"""
实时转录服务 - 轻量级版本，避免VAD错误
"""
import numpy as np
from funasr import AutoModel
import time
from config import MODEL_CONFIG, REALTIME_CONFIG
from .participant_audio_manager import ParticipantAudioManager


class RealtimeAudioTranscriptionService:
    """实时音频转录服务 - 专门为实时转录优化，避免VAD索引错误"""

    def __init__(self):
        print("🔧 初始化实时转录服务...")
        self.models = self._load_realtime_models()
        self.participant_manager = ParticipantAudioManager(self.models.get("speaker"))
        print("✅ 实时转录服务初始化完成")

    def _load_realtime_models(self):
        """加载实时模型 - 不包含VAD，避免索引错误"""
        print("正在加载实时模型（无VAD，避免索引错误）...")

        # 仅ASR模型，不包含VAD和标点（避免实时转录错误）
        try:
            asr_model = AutoModel(
                model=MODEL_CONFIG["asr_model_path"],
                device=MODEL_CONFIG["device"],
                disable_update=True,
                disable_pbar=True
            )
            print("✓ 实时ASR模型加载成功（无VAD）")
        except Exception as e:
            print(f"实时ASR模型加载失败: {e}")
            raise e

        # 说话人模型（可选）
        speaker_model = None
        speaker_model_type = None

        try:
            print("尝试加载实时说话人模型...")
            from modelscope.pipelines import pipeline
            from modelscope.utils.constant import Tasks

            speaker_model = pipeline(
                task=Tasks.speaker_verification,
                model=MODEL_CONFIG["speaker_model_path"],
                device=MODEL_CONFIG["device"]
            )
            speaker_model_type = "3d_speaker_local"
            print("✓ 实时说话人模型加载成功")

        except Exception as e:
            print(f"实时说话人模型加载失败: {e}")
            print("实时转录将使用简单说话人分离")

        return {
            "asr": asr_model,
            "speaker": speaker_model,
            "speaker_type": speaker_model_type
        }

    def transcribe_audio_simple(self, audio_data, sample_rate):
        """实时简单转录 - 专门优化，避免VAD错误"""
        try:
            print(f"开始实时简单转录 - 音频长度: {len(audio_data)}, 采样率: {sample_rate}")

            # 检查音频长度，太短的音频直接返回空结果
            min_samples = int(REALTIME_CONFIG["min_audio_duration"] * sample_rate)
            if len(audio_data) < min_samples:
                print(f"音频太短 ({len(audio_data)} < {min_samples})，跳过转录")
                return {
                    "text": "",
                    "duration": len(audio_data) / sample_rate,
                    "sample_rate": sample_rate
                }

            # 预处理音频
            audio_data = self._preprocess_realtime_audio(audio_data, sample_rate)

            # 使用无VAD的FunASR进行转录
            print("正在进行实时ASR转录...")
            try:
                # 强制禁用所有可能导致索引错误的参数
                result = self.models["asr"].generate(
                    input=audio_data,
                    batch_size_s=REALTIME_CONFIG["batch_size_s"],
                    hotword="",
                    use_itn=REALTIME_CONFIG["use_itn"],
                    disable_pbar=True
                )
            except Exception as e:
                print(f"实时转录失败: {e}")
                # 如果还是失败，尝试最简单的方式
                try:
                    result = self.models["asr"].generate(input=audio_data, disable_pbar=True)
                except Exception as e2:
                    print(f"简单实时转录也失败: {e2}")
                    return {"error": f"实时转录失败: {str(e2)}"}

            # 提取文本
            full_text = self._extract_text_from_result(result)
            print(f"✓ 实时简单转录完成 - 文本长度: {len(full_text)}")

            return {
                "text": full_text.strip(),
                "duration": len(audio_data) / REALTIME_CONFIG["sample_rate"],
                "sample_rate": REALTIME_CONFIG["sample_rate"]
            }

        except Exception as e:
            print(f"❌ 实时简单转录失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}

    def transcribe_audio_with_speaker(self, audio_data, sample_rate, session_id=None):
        """实时转录并进行说话人分离"""
        try:
            print(f"开始实时说话人分离转录 - 音频长度: {len(audio_data)}, 采样率: {sample_rate}")

            # 检查音频长度
            min_samples = int(REALTIME_CONFIG["min_audio_duration"] * sample_rate)
            if len(audio_data) < min_samples:
                print(f"音频太短，跳过转录")
                return {
                    "text": "",
                    "segments": [],
                    "duration": len(audio_data) / sample_rate,
                    "sample_rate": sample_rate
                }

            # 预处理音频
            audio_data = self._preprocess_realtime_audio(audio_data, sample_rate)

            # 使用无VAD的FunASR进行转录
            print("正在进行实时ASR转录...")
            try:
                result = self.models["asr"].generate(
                    input=audio_data,
                    batch_size_s=REALTIME_CONFIG["batch_size_s"],
                    hotword="",
                    use_itn=REALTIME_CONFIG["use_itn"],
                    disable_pbar=True
                )
            except Exception as e:
                print(f"实时转录失败: {e}")
                try:
                    result = self.models["asr"].generate(input=audio_data, disable_pbar=True)
                except Exception as e2:
                    print(f"简单实时转录也失败: {e2}")
                    return {"error": f"实时转录失败: {str(e2)}"}

            # 处理说话人分离
            segments = self._process_realtime_speaker_diarization(audio_data, result, REALTIME_CONFIG["sample_rate"], session_id)

            # 提取完整文本
            full_text = self._extract_text_from_result(result)

            print(f"✓ 实时说话人分离转录完成 - 文本长度: {len(full_text)}, 分段数: {len(segments)}")

            return {
                "text": full_text.strip(),
                "segments": segments,
                "duration": len(audio_data) / REALTIME_CONFIG["sample_rate"],
                "sample_rate": REALTIME_CONFIG["sample_rate"]
            }

        except Exception as e:
            print(f"❌ 实时说话人分离转录失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}

    def _preprocess_realtime_audio(self, audio_data, sample_rate):
        """预处理实时音频"""
        # 确保音频是单声道
        if len(audio_data.shape) > 1:
            audio_data = np.mean(audio_data, axis=1)

        # 重采样到16kHz（如果需要）
        if sample_rate != REALTIME_CONFIG["sample_rate"]:
            print(f"重采样从 {sample_rate}Hz 到 {REALTIME_CONFIG['sample_rate']}Hz")
            import librosa
            audio_data = librosa.resample(
                audio_data,
                orig_sr=sample_rate,
                target_sr=REALTIME_CONFIG["sample_rate"]
            )

        # 确保音频数据是连续的numpy数组
        audio_data = np.ascontiguousarray(audio_data, dtype=np.float32)

        # 音频归一化
        if np.max(np.abs(audio_data)) > 0:
            audio_data = audio_data / np.max(np.abs(audio_data)) * 0.9

        return audio_data

    def _extract_text_from_result(self, result):
        """从ASR结果中提取文本"""
        full_text = ""
        if isinstance(result, list) and len(result) > 0:
            if isinstance(result[0], dict) and 'text' in result[0]:
                full_text = result[0]['text']
            else:
                full_text = " ".join([str(r.get('text', '')) for r in result if isinstance(r, dict)])
        elif isinstance(result, dict) and 'text' in result:
            full_text = result['text']
        return full_text

    def _process_realtime_speaker_diarization(self, audio_data, asr_result, sample_rate, session_id=None):
        """处理实时说话人分离 - 集成参会人识别"""
        segments = []

        try:
            # 尝试识别参会人
            identified_speaker = None
            if session_id and self.participant_manager.get_participant_list(session_id):
                identified_speaker = self.participant_manager.identify_speaker(session_id, audio_data, sample_rate)

            # 处理ASR结果
            if isinstance(asr_result, list):
                for i, item in enumerate(asr_result):
                    if isinstance(item, dict):
                        text = item.get('text', '').strip()
                        if text:
                            # 使用识别出的参会人名称，否则使用默认Speaker_X
                            if identified_speaker:
                                speaker_id = identified_speaker
                            else:
                                speaker_id = f"Speaker_{i % 2}"

                            start_time = item.get('timestamp', [[0, len(text)]])[0][
                                             0] / 1000 if 'timestamp' in item else i * 3
                            end_time = item.get('timestamp', [[0, len(text)]])[-1][
                                           1] / 1000 if 'timestamp' in item else (i + 1) * 3

                            segments.append({
                                "start": start_time,
                                "end": end_time,
                                "text": text,
                                "speaker": speaker_id
                            })
            else:
                # 单个结果的情况
                text = asr_result.get('text', '').strip() if isinstance(asr_result, dict) else str(asr_result).strip()
                if text:
                    # 使用识别出的参会人名称，否则使用默认Speaker_0
                    speaker_id = identified_speaker if identified_speaker else "Speaker_0"

                    segments.append({
                        "start": 0,
                        "end": len(audio_data) / sample_rate,
                        "text": text,
                        "speaker": speaker_id
                    })

        except Exception as e:
            print(f"实时说话人分离处理失败: {e}")
            # 回退到简单分段
            text = self._extract_text_from_result(asr_result)
            if text.strip():
                segments.append({
                    "start": 0,
                    "end": len(audio_data) / sample_rate,
                    "text": text.strip(),
                    "speaker": "Speaker_0"
                })

        return segments