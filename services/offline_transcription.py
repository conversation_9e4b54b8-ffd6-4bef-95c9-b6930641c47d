"""
离线转录服务 - 完整功能版本
"""
import os
import json
import numpy as np
import soundfile as sf
from funasr import AutoModel
import io
import subprocess
import tempfile
import uuid
from pathlib import Path
from config import MODEL_CONFIG, OFFLINE_CONFIG


class OfflineAudioTranscriptionService:
    """离线音频转录服务 - 包含完整的VAD、标点、说话人分离功能"""

    def __init__(self):
        print("🔧 初始化离线转录服务...")
        self.models = self._load_offline_models()
        print("✅ 离线转录服务初始化完成")

    def _load_offline_models(self):
        """加载离线模型 - 包含VAD和标点"""
        print("正在加载离线模型（包含VAD和标点）...")

        # 完整的ASR模型（包含VAD和标点）
        asr_model = AutoModel(
            model=MODEL_CONFIG["asr_model_path"],
            vad_model=MODEL_CONFIG["vad_model_path"],
            punc_model=MODEL_CONFIG["punc_model_path"],
            device=MODEL_CONFIG["device"],
            disable_update=True
        )

        # 说话人模型加载
        speaker_model = None
        speaker_model_type = None

        try:
            print("尝试加载3D-Speaker模型...")
            from modelscope.pipelines import pipeline
            from modelscope.utils.constant import Tasks

            speaker_model = pipeline(
                task=Tasks.speaker_verification,
                model=MODEL_CONFIG["speaker_model_path"],
                device=MODEL_CONFIG["device"]
            )
            speaker_model_type = "3d_speaker_local"
            print("✓ 本地3D-Speaker模型加载成功")

        except Exception as e:
            print(f"3D-Speaker模型加载失败: {e}")
            print("将使用简单说话人分离")

        return {
            "asr": asr_model,
            "speaker": speaker_model,
            "speaker_type": speaker_model_type
        }

    def transcribe_audio_simple(self, audio_data, sample_rate):
        """简单转录音频 - 仅返回文本，不进行说话人分离"""
        try:
            print(f"开始离线简单转录 - 音频长度: {len(audio_data)}, 采样率: {sample_rate}")

            # 确保音频是单声道
            if len(audio_data.shape) > 1:
                audio_data = np.mean(audio_data, axis=1)

            # 重采样到16kHz（如果需要）
            if sample_rate != OFFLINE_CONFIG["sample_rate"]:
                print(f"重采样从 {sample_rate}Hz 到 {OFFLINE_CONFIG['sample_rate']}Hz")
                import librosa
                audio_data = librosa.resample(audio_data, orig_sr=sample_rate, target_sr=OFFLINE_CONFIG["sample_rate"])
                sample_rate = OFFLINE_CONFIG["sample_rate"]

            # 使用完整的FunASR进行转录（包含VAD和标点）
            print("正在进行离线ASR转录...")
            result = self.models["asr"].generate(
                input=audio_data,
                batch_size_s=OFFLINE_CONFIG["batch_size_s"],
                hotword="",
                use_itn=OFFLINE_CONFIG["use_itn"]
            )

            # 处理简单分段
            segments = self._process_simple_segments(result)

            # 提取完整文本
            full_text = ""
            if isinstance(result, list) and len(result) > 0:
                if isinstance(result[0], dict) and 'text' in result[0]:
                    full_text = result[0]['text']
                else:
                    full_text = " ".join([str(r.get('text', '')) for r in result if isinstance(r, dict)])
            elif isinstance(result, dict) and 'text' in result:
                full_text = result['text']

            print(f"✓ 离线简单转录完成 - 文本长度: {len(full_text)}")

            return {
                "text": full_text.strip(),
                "duration": len(audio_data) / sample_rate,
                "sample_rate": sample_rate
            }

        except Exception as e:
            print(f"❌ 离线简单转录失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}

    def transcribe_audio_with_speaker(self, audio_data, sample_rate):
        """转录音频并进行说话人分离"""
        try:
            print(f"开始离线说话人分离转录 - 音频长度: {len(audio_data)}, 采样率: {sample_rate}")

            # 确保音频是单声道
            if len(audio_data.shape) > 1:
                audio_data = np.mean(audio_data, axis=1)

            # 重采样到16kHz（如果需要）
            if sample_rate != OFFLINE_CONFIG["sample_rate"]:
                print(f"重采样从 {sample_rate}Hz 到 {OFFLINE_CONFIG['sample_rate']}Hz")
                import librosa
                audio_data = librosa.resample(audio_data, orig_sr=sample_rate, target_sr=OFFLINE_CONFIG["sample_rate"])
                sample_rate = OFFLINE_CONFIG["sample_rate"]

            # 使用完整的FunASR进行转录
            print("正在进行离线ASR转录...")
            result = self.models["asr"].generate(
                input=audio_data,
                batch_size_s=OFFLINE_CONFIG["batch_size_s"],
                hotword="",
                use_itn=OFFLINE_CONFIG["use_itn"]
            )

            # 处理说话人分离
            segments = self._process_speaker_diarization(audio_data, result, sample_rate)

            # 提取完整文本
            full_text = ""
            if isinstance(result, list) and len(result) > 0:
                if isinstance(result[0], dict) and 'text' in result[0]:
                    full_text = result[0]['text']
                else:
                    full_text = " ".join([str(r.get('text', '')) for r in result if isinstance(r, dict)])
            elif isinstance(result, dict) and 'text' in result:
                full_text = result['text']

            # 最终验证：确保分段按时间排序
            segments = self._validate_and_fix_segments(segments, len(audio_data) / sample_rate)

            print(f"✓ 离线说话人分离转录完成 - 文本长度: {len(full_text)}, 分段数: {len(segments)}")

            return {
                "text": full_text.strip(),
                "segments": segments,
                "duration": len(audio_data) / sample_rate,
                "sample_rate": sample_rate
            }

        except Exception as e:
            print(f"❌ 离线说话人分离转录失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}

    def _process_simple_segments(self, result):
        """处理简单分段"""
        segments = []

        try:
            if isinstance(result, list):
                for i, item in enumerate(result):
                    if isinstance(item, dict):
                        text = item.get('text', '').strip()
                        if text:
                            start_time = item.get('timestamp', [[0, len(text)]])[0][
                                             0] / 1000 if 'timestamp' in item else i * 5
                            end_time = item.get('timestamp', [[0, len(text)]])[-1][
                                           1] / 1000 if 'timestamp' in item else (i + 1) * 5

                            segments.append({
                                "start": start_time,
                                "end": end_time,
                                "text": text,
                                "speaker": "Speaker_0"
                            })
            elif isinstance(result, dict):
                text = result.get('text', '').strip()
                if text:
                    segments.append({
                        "start": 0,
                        "end": len(text) * 0.1,
                        "text": text,
                        "speaker": "Speaker_0"
                    })
        except Exception as e:
            print(f"处理简单分段时出错: {e}")
            if isinstance(result, list) and len(result) > 0:
                text = str(result[0].get('text', '')) if isinstance(result[0], dict) else str(result[0])
            elif isinstance(result, dict):
                text = result.get('text', '')
            else:
                text = str(result)

            if text.strip():
                segments.append({
                    "start": 0,
                    "end": len(text) * 0.1,
                    "text": text.strip(),
                    "speaker": "Speaker_0"
                })

        return self._sort_segments_by_time(segments)

    def _process_speaker_diarization(self, audio_data, asr_result, sample_rate):
        """处理说话人分离"""
        segments = []

        try:
            if self.models["speaker"] is not None:
                print("正在进行说话人分离...")
                if isinstance(asr_result, list):
                    for i, item in enumerate(asr_result):
                        if isinstance(item, dict):
                            text = item.get('text', '').strip()
                            if text:
                                speaker_id = f"Speaker_{i % 2}"
                                start_time = item.get('timestamp', [[0, len(text)]])[0][
                                                 0] / 1000 if 'timestamp' in item else i * 5
                                end_time = item.get('timestamp', [[0, len(text)]])[-1][
                                               1] / 1000 if 'timestamp' in item else (i + 1) * 5

                                segments.append({
                                    "start": start_time,
                                    "end": end_time,
                                    "text": text,
                                    "speaker": speaker_id
                                })
                else:
                    text = asr_result.get('text', '').strip() if isinstance(asr_result, dict) else str(
                        asr_result).strip()
                    if text:
                        segments.append({
                            "start": 0,
                            "end": len(audio_data) / sample_rate,
                            "text": text,
                            "speaker": "Speaker_0"
                        })
            else:
                segments = self._process_simple_segments(asr_result)

        except Exception as e:
            print(f"说话人分离处理失败: {e}")
            segments = self._process_simple_segments(asr_result)

        return self._sort_segments_by_time(segments)

    def _sort_segments_by_time(self, segments):
        """按时间顺序排序分段"""
        if not segments:
            return segments

        print(f"排序前分段数量: {len(segments)}")
        sorted_segments = sorted(segments, key=lambda x: x.get('start', 0))

        fixed_segments = []
        for i, current_segment in enumerate(sorted_segments):
            if current_segment.get('end', 0) <= current_segment['start']:
                text_length = len(current_segment.get('text', ''))
                estimated_duration = max(0.5, text_length * 0.1)
                current_segment['end'] = current_segment['start'] + estimated_duration
                print(f"修复时长: 分段{i} 结束时间调整为 {current_segment['end']:.2f}s")

            fixed_segments.append(current_segment)

        print(f"排序后分段数量: {len(fixed_segments)}")
        return fixed_segments

    def _validate_and_fix_segments(self, segments, total_duration):
        """验证并修复分段时间"""
        print("最终验证分段时间...")

        validated_segments = []
        for i, segment in enumerate(segments):
            validated_segment = segment.copy()

            validated_segment['start'] = max(0, min(validated_segment.get('start', 0), total_duration))
            validated_segment['end'] = max(validated_segment['start'],
                                           min(validated_segment.get('end', 0), total_duration))

            if validated_segment['end'] <= validated_segment['start']:
                text_length = len(validated_segment.get('text', ''))
                estimated_duration = max(0.1, text_length * 0.08)
                validated_segment['end'] = min(validated_segment['start'] + estimated_duration, total_duration)

            validated_segments.append(validated_segment)

        return validated_segments