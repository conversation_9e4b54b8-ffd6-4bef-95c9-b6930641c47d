#!/usr/bin/env python3
"""
参会人音频管理服务
处理参会人音频文件的上传、存储、特征提取和匹配
"""

import os
import json
import numpy as np
import soundfile as sf
import tempfile
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import hashlib
import time

from config import MODEL_CONFIG


class ParticipantAudioManager:
    """参会人音频管理器"""
    
    def __init__(self, speaker_model=None):
        self.speaker_model = speaker_model
        self.participant_embeddings = {}  # 存储参会人的音频特征
        self.similarity_threshold = 0.6  # 相似度阈值
        
    def add_participant_audio(self, name: str, audio_data: np.ndarray, sample_rate: int) -> bool:
        """
        添加参会人音频并提取特征
        
        Args:
            name: 参会人姓名
            audio_data: 音频数据
            sample_rate: 采样率
            
        Returns:
            bool: 是否成功添加
        """
        try:
            print(f"正在为参会人 '{name}' 提取音频特征...")
            
            # 预处理音频
            processed_audio = self._preprocess_audio(audio_data, sample_rate)
            
            # 提取音频特征
            embedding = self._extract_speaker_embedding(processed_audio)
            
            if embedding is not None:
                self.participant_embeddings[name] = {
                    'embedding': embedding,
                    'timestamp': time.time()
                }
                print(f"✓ 参会人 '{name}' 音频特征提取成功")
                return True
            else:
                print(f"❌ 参会人 '{name}' 音频特征提取失败")
                return False
                
        except Exception as e:
            print(f"❌ 添加参会人音频失败: {str(e)}")
            return False
    
    def add_participant_audios_from_files(self, audio_files: Dict[str, any]) -> Dict[str, bool]:
        """
        从上传的文件中批量添加参会人音频
        
        Args:
            audio_files: {filename: file_object} 字典
            
        Returns:
            Dict[str, bool]: 每个参会人的处理结果
        """
        results = {}
        
        for filename, file_obj in audio_files.items():
            try:
                # 从文件名提取参会人姓名（去掉扩展名）
                name = Path(filename).stem
                
                # 转换音频文件
                audio_data, sample_rate = self._convert_audio_file(file_obj)
                
                # 添加参会人音频
                success = self.add_participant_audio(name, audio_data, sample_rate)
                results[name] = success
                
            except Exception as e:
                print(f"❌ 处理参会人音频文件 '{filename}' 失败: {str(e)}")
                results[Path(filename).stem] = False
                
        return results
    
    def identify_speaker(self, audio_segment: np.ndarray, sample_rate: int) -> Optional[str]:
        """
        识别音频片段中的说话人
        
        Args:
            audio_segment: 音频片段
            sample_rate: 采样率
            
        Returns:
            Optional[str]: 识别出的参会人姓名，如果没有匹配则返回None
        """
        try:
            if not self.participant_embeddings:
                return None
                
            # 预处理音频
            processed_audio = self._preprocess_audio(audio_segment, sample_rate)
            
            # 提取音频特征
            segment_embedding = self._extract_speaker_embedding(processed_audio)
            
            if segment_embedding is None:
                return None
            
            # 与所有参会人特征进行比较
            best_match = None
            best_similarity = -1
            
            for name, participant_data in self.participant_embeddings.items():
                participant_embedding = participant_data['embedding']
                similarity = self._calculate_similarity(segment_embedding, participant_embedding)
                
                if similarity > best_similarity:
                    best_similarity = similarity
                    best_match = name
            
            # 检查是否超过阈值
            if best_similarity >= self.similarity_threshold:
                print(f"🎯 识别出说话人: {best_match} (相似度: {best_similarity:.3f})")
                return best_match
            else:
                print(f"⚠️ 未找到匹配的参会人 (最高相似度: {best_similarity:.3f})")
                return None
                
        except Exception as e:
            print(f"❌ 说话人识别失败: {str(e)}")
            return None
    
    def get_participant_list(self) -> List[str]:
        """获取已注册的参会人列表"""
        return list(self.participant_embeddings.keys())
    
    def clear_participants(self):
        """清空所有参会人数据"""
        self.participant_embeddings.clear()
        print("✓ 已清空所有参会人数据")
    
    def _preprocess_audio(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """预处理音频数据"""
        # 确保音频是单声道
        if len(audio_data.shape) > 1:
            audio_data = np.mean(audio_data, axis=1)
        
        # 重采样到16kHz（如果需要）
        if sample_rate != 16000:
            import librosa
            audio_data = librosa.resample(audio_data, orig_sr=sample_rate, target_sr=16000)
        
        # 音频长度检查和截取
        min_length = int(16000 * 1.0)  # 至少1秒
        max_length = int(16000 * 10.0)  # 最多10秒
        
        if len(audio_data) < min_length:
            # 如果太短，进行填充
            audio_data = np.pad(audio_data, (0, min_length - len(audio_data)), mode='constant')
        elif len(audio_data) > max_length:
            # 如果太长，截取中间部分
            start = (len(audio_data) - max_length) // 2
            audio_data = audio_data[start:start + max_length]
        
        return audio_data
    
    def _extract_speaker_embedding(self, audio_data: np.ndarray) -> Optional[np.ndarray]:
        """提取说话人特征向量"""
        try:
            if self.speaker_model is None:
                print("⚠️ 说话人模型未加载，无法提取特征")
                return None

            # 使用3D-Speaker模型提取特征
            # 3D-Speaker模型通常需要两个音频进行比较，这里我们使用单个音频提取embedding
            try:
                # 尝试直接调用模型获取embedding
                if hasattr(self.speaker_model, 'extract_embedding'):
                    result = self.speaker_model.extract_embedding(audio_data)
                elif hasattr(self.speaker_model, 'forward'):
                    result = self.speaker_model.forward(audio_data)
                else:
                    # 如果是pipeline，尝试使用两个相同的音频
                    result = self.speaker_model([audio_data, audio_data])

                # 处理不同格式的结果
                if isinstance(result, dict):
                    if 'embedding' in result:
                        return result['embedding']
                    elif 'embeddings' in result:
                        embeddings = result['embeddings']
                        if isinstance(embeddings, list) and len(embeddings) > 0:
                            return embeddings[0]
                        return embeddings
                    elif 'score' in result:
                        # 如果只返回相似度分数，我们无法获取embedding
                        print("⚠️ 模型只返回相似度分数，无法获取embedding")
                        return None
                elif isinstance(result, np.ndarray):
                    return result
                elif isinstance(result, list) and len(result) > 0:
                    return result[0] if isinstance(result[0], np.ndarray) else None
                else:
                    print(f"⚠️ 未知的特征提取结果格式: {type(result)}")
                    return None

            except Exception as model_error:
                print(f"⚠️ 模型调用失败: {str(model_error)}")
                # 如果模型调用失败，返回一个简单的音频特征作为fallback
                return self._extract_simple_audio_features(audio_data)

        except Exception as e:
            print(f"❌ 特征提取失败: {str(e)}")
            return None

    def _extract_simple_audio_features(self, audio_data: np.ndarray) -> np.ndarray:
        """提取简单的音频特征作为fallback"""
        try:
            # 计算简单的统计特征
            features = []

            # 基本统计特征
            features.extend([
                np.mean(audio_data),
                np.std(audio_data),
                np.max(audio_data),
                np.min(audio_data)
            ])

            # 频域特征（简化版）
            fft = np.fft.fft(audio_data)
            magnitude = np.abs(fft)[:len(fft)//2]

            # 频谱质心
            freqs = np.arange(len(magnitude))
            spectral_centroid = np.sum(freqs * magnitude) / np.sum(magnitude) if np.sum(magnitude) > 0 else 0
            features.append(spectral_centroid)

            # 频谱带宽
            spectral_bandwidth = np.sqrt(np.sum(((freqs - spectral_centroid) ** 2) * magnitude) / np.sum(magnitude)) if np.sum(magnitude) > 0 else 0
            features.append(spectral_bandwidth)

            # 零交叉率
            zero_crossings = np.sum(np.diff(np.sign(audio_data)) != 0)
            features.append(zero_crossings / len(audio_data))

            # 能量
            energy = np.sum(audio_data ** 2) / len(audio_data)
            features.append(energy)

            return np.array(features, dtype=np.float32)

        except Exception as e:
            print(f"❌ 简单特征提取失败: {str(e)}")
            # 返回随机特征向量作为最后的fallback
            return np.random.rand(8).astype(np.float32)
    
    def _calculate_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """计算两个特征向量的相似度"""
        try:
            # 使用余弦相似度
            dot_product = np.dot(embedding1, embedding2)
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            print(f"❌ 相似度计算失败: {str(e)}")
            return 0.0
    
    def _convert_audio_file(self, file_obj) -> Tuple[np.ndarray, int]:
        """转换音频文件为numpy数组"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.tmp') as temp_input:
                file_obj.seek(0)
                temp_input.write(file_obj.read())
                temp_input_path = temp_input.name

            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_wav:
                temp_wav_path = temp_wav.name

            # 使用ffmpeg转换
            cmd = [
                'ffmpeg', '-i', temp_input_path,
                '-ar', '16000', '-ac', '1', '-f', 'wav', '-y', temp_wav_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception(f"ffmpeg转换失败: {result.stderr}")

            # 读取转换后的音频
            audio_data, sample_rate = sf.read(temp_wav_path)

            # 清理临时文件
            os.unlink(temp_input_path)
            os.unlink(temp_wav_path)

            return audio_data, sample_rate

        except Exception as e:
            # 清理可能存在的临时文件
            for path in [temp_input_path, temp_wav_path]:
                if 'path' in locals() and os.path.exists(path):
                    os.unlink(path)
            raise e
