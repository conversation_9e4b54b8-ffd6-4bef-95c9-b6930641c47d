#!/usr/bin/env python3
"""
测试服务器启动脚本
用于测试会话级参会人音频识别功能
"""

import os
import sys
import time
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        'flask',
        'flask_socketio',
        'numpy',
        'soundfile',
        'funasr',
        'modelscope'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_models():
    """检查模型文件是否存在"""
    model_dirs = [
        'models/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch',
        'models/speech_eres2net_large_sv_zh-cn_3dspeaker_16k',
        'models/speech_fsmn_vad_zh-cn-16k-common-pytorch',
        'models/punc_ct-transformer_zh-cn-common-vocab272727-pytorch'
    ]
    
    missing_models = []
    for model_dir in model_dirs:
        if not Path(model_dir).exists():
            missing_models.append(model_dir)
    
    if missing_models:
        print(f"⚠️ 以下模型目录不存在: {missing_models}")
        print("模型将在首次运行时自动下载")
    else:
        print("✅ 所有模型目录都存在")
    
    return True

def start_server():
    """启动测试服务器"""
    print("🚀 启动会话级参会人音频识别测试服务器...")
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 检查模型
    check_models()
    
    # 设置环境变量
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = '1'
    
    try:
        # 导入并启动应用
        from app import app, socketio
        
        print("✅ 应用加载成功")
        print("📋 测试页面:")
        print("  - 主页面: http://localhost:5000/")
        print("  - 会话级测试: http://localhost:5000/test_session_participants.html")
        print("  - 简单测试: http://localhost:5000/test_simple.html")
        print()
        print("🎯 测试步骤:")
        print("1. 准备一些音频文件（WAV格式推荐）")
        print("2. 将参会人音频文件命名为姓名（如：张三.wav）")
        print("3. 打开测试页面，上传主音频和参会人音频样本")
        print("4. 启动转录，查看是否能正确识别参会人")
        print()
        print("按 Ctrl+C 停止服务器")
        print("-" * 50)
        
        # 启动服务器
        socketio.run(app, host='0.0.0.0', port=5000, debug=True)
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保所有依赖都已正确安装")
        return False
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🎙️ 会话级参会人音频识别测试")
    print("=" * 50)
    
    if not start_server():
        sys.exit(1)
