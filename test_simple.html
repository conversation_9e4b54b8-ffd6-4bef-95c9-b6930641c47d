<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参会人音频识别测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .success {
            color: #28a745;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .participant-file {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            gap: 10px;
        }
        .participant-file input {
            flex: 1;
        }
        .participant-file button {
            padding: 5px 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🎙️ 参会人音频识别测试</h1>
    
    <form id="testForm" enctype="multipart/form-data">
        <div class="form-group">
            <label for="audioFile">主音频文件:</label>
            <input type="file" id="audioFile" name="audio" accept="audio/*" required>
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" id="enableSpeaker" checked>
                启用说话人识别
            </label>
        </div>

        <div id="participantSection">
            <div class="form-group">
                <label>参会人音频文件 (以姓名命名):</label>
                <div id="participantFiles">
                    <div class="participant-file">
                        <input type="file" name="participant_0" accept="audio/*" placeholder="张三.wav">
                        <button type="button" onclick="addParticipantFile()">+ 添加</button>
                    </div>
                </div>
                <small style="color: #666;">
                    提示：将音频文件命名为参会人姓名（如：张三.wav），系统会自动识别
                </small>
            </div>
        </div>

        <button type="submit">开始转录</button>
    </form>

    <div id="result" class="result" style="display: none;"></div>

    <script>
        let participantCount = 1;

        function addParticipantFile() {
            const container = document.getElementById('participantFiles');
            const newFile = document.createElement('div');
            newFile.className = 'participant-file';
            newFile.innerHTML = `
                <input type="file" name="participant_${participantCount}" accept="audio/*">
                <button type="button" onclick="removeParticipantFile(this)">- 删除</button>
            `;
            container.appendChild(newFile);
            participantCount++;
        }

        function removeParticipantFile(button) {
            button.parentElement.remove();
        }

        document.getElementById('enableSpeaker').addEventListener('change', function() {
            const section = document.getElementById('participantSection');
            section.style.display = this.checked ? 'block' : 'none';
        });

        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const audioFile = document.getElementById('audioFile').files[0];
            const enableSpeaker = document.getElementById('enableSpeaker').checked;
            
            if (!audioFile) {
                showResult('请选择主音频文件', 'error');
                return;
            }
            
            formData.append('audio', audioFile);
            formData.append('enable_speaker', enableSpeaker);
            
            // 添加参会人音频文件
            if (enableSpeaker) {
                const participantInputs = document.querySelectorAll('#participantFiles input[type="file"]');
                participantInputs.forEach((input, index) => {
                    if (input.files[0]) {
                        formData.append(`participant_${index}`, input.files[0]);
                    }
                });
            }
            
            showResult('🔄 正在处理，请稍候...', 'info');
            
            try {
                const response = await fetch('/transcribe', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    let html = '<div class="success">✅ 转录成功</div>';
                    
                    // 显示参会人处理结果
                    if (result.data.participant_results && Object.keys(result.data.participant_results).length > 0) {
                        html += '<h3>👥 参会人音频处理结果:</h3>';
                        for (const [name, success] of Object.entries(result.data.participant_results)) {
                            const status = success ? '✅ 成功' : '❌ 失败';
                            html += `<p><strong>${name}:</strong> ${status}</p>`;
                        }
                    }
                    
                    // 显示转录结果
                    if (result.data.segmented_text && result.data.segmented_text.length > 0) {
                        html += '<h3>🎭 说话人分离结果:</h3>';
                        result.data.segmented_text.forEach(segment => {
                            html += `<div style="margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 4px;">
                                <strong>${segment.speaker}</strong> 
                                <span style="color: #666;">(${segment.start_time.toFixed(1)}s-${segment.end_time.toFixed(1)}s)</span>: 
                                ${segment.text}
                            </div>`;
                        });
                    } else {
                        html += `<h3>📝 转录结果:</h3><p>${result.data.voice_text}</p>`;
                    }
                    
                    showResult(html, 'success');
                } else {
                    showResult(`❌ 转录失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 请求失败: ${error.message}`, 'error');
            }
        });

        function showResult(content, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = content;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
